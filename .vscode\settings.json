{"idf.openOcdConfigs": ["board/esp32c3-builtin.cfg"], "idf.customExtraVars": {"IDF_TARGET": "esp32c3"}, "clangd.path": "e:\\esp_tool\\tools\\esp-clang\\16.0.1-fe4f10a809\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=e:\\esp_tool\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe", "--compile-commands-dir=e:\\esp32_space\\adc\\oneshot_read\\build"], "idf.flashType": "UART", "idf.portWin": "COM30"}